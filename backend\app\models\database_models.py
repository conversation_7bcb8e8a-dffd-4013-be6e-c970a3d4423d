from sqlalchemy import Column, Integer, String, DateTime, Decimal, Text, Boolean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class Branch(Base):
    __tablename__ = "branch"

    id_branch = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(45), nullable=False)
    warehouse = Column(String(45))
    active = Column(Boolean, default=True)
    short_name = Column(String(10))
    email = Column(String(120))
    address1 = Column(String(45))
    address2 = Column(String(45))
    phone = Column(String(20))
    mobile = Column(String(20))

class Category(Base):
    __tablename__ = "category"

    id_category = Column(Integer, primary_key=True, autoincrement=True)
    id_parent = Column(Integer, default=0)
    categoryname = Column(String(125))
    description = Column(String(245))
    catimage = Column(String(120))
    active = Column(Boolean, default=True)
    date_add = Column(DateTime, default=datetime.utcnow)

class Product(Base):
    __tablename__ = "product"

    id_product = Column(Integer, primary_key=True, autoincrement=True)
    id_category = Column(Integer, ForeignKey("category.id_category"))
    productname = Column(String(125))
    description = Column(Text)
    active = Column(Boolean, default=True)
    date_add = Column(DateTime, default=datetime.utcnow)

class Design(Base):
    __tablename__ = "design"

    id_design = Column(Integer, primary_key=True, autoincrement=True)
    id_product = Column(Integer, ForeignKey("product.id_product"))
    designname = Column(String(125))
    description = Column(Text)
    active = Column(Boolean, default=True)
    date_add = Column(DateTime, default=datetime.utcnow)

class SubDesign(Base):
    __tablename__ = "sub_design"

    id_sub_design = Column(Integer, primary_key=True, autoincrement=True)
    id_design = Column(Integer, ForeignKey("design.id_design"))
    sub_designname = Column(String(125))
    description = Column(Text)
    active = Column(Boolean, default=True)
    date_add = Column(DateTime, default=datetime.utcnow)

class Tag(Base):
    __tablename__ = "tag"

    id_tag = Column(Integer, primary_key=True, autoincrement=True)
    tag_no = Column(String(50), unique=True, nullable=False)
    id_branch = Column(Integer, ForeignKey("branch.id_branch"))
    id_category = Column(Integer, ForeignKey("category.id_category"))
    id_product = Column(Integer, ForeignKey("product.id_product"))
    id_design = Column(Integer, ForeignKey("design.id_design"))
    id_sub_design = Column(Integer, ForeignKey("sub_design.id_sub_design"))
    gross_weight = Column(Decimal(10, 3))
    net_weight = Column(Decimal(10, 3))
    stone_weight = Column(Decimal(10, 3))
    purity = Column(Decimal(5, 2))
    making_charge = Column(Decimal(10, 2))
    stone_charge = Column(Decimal(10, 2))
    other_charge = Column(Decimal(10, 2))
    total_amount = Column(Decimal(12, 2))
    status = Column(Integer, default=1)  # 1=Available, 2=Sold, 3=Reserved
    date_add = Column(DateTime, default=datetime.utcnow)
    date_upd = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    branch = relationship("Branch")
    category = relationship("Category")
    product = relationship("Product")
    design = relationship("Design")
    sub_design = relationship("SubDesign")

# New tables for tag import functionality
class TagImportBatch(Base):
    __tablename__ = "tag_import_batch"

    id_batch = Column(Integer, primary_key=True, autoincrement=True)
    batch_name = Column(String(100), nullable=False)
    file_name = Column(String(255), nullable=False)
    file_path = Column(String(500))
    total_rows = Column(Integer, default=0)
    processed_rows = Column(Integer, default=0)
    success_rows = Column(Integer, default=0)
    error_rows = Column(Integer, default=0)
    status = Column(String(20), default="PENDING")  # PENDING, PROCESSING, COMPLETED, FAILED
    created_by = Column(String(50))
    created_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime)
    error_message = Column(Text)

class TagImportError(Base):
    __tablename__ = "tag_import_error"

    id_error = Column(Integer, primary_key=True, autoincrement=True)
    id_batch = Column(Integer, ForeignKey("tag_import_batch.id_batch"))
    row_number = Column(Integer)
    column_name = Column(String(100))
    error_type = Column(String(50))  # VALIDATION, DUPLICATE, MISSING_REF, etc.
    error_message = Column(Text)
    row_data = Column(Text)  # JSON string of the row data
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationship
    batch = relationship("TagImportBatch")
